import React, { useEffect, useCallback, useState } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Markdown } from 'tiptap-markdown';

interface TiptapInlineEditorProps {
    initialValue?: string;
    onSave?: (value: string) => void;
    placeholder?: string;
}

export function TiptapInlineEditor({
    initialValue = '',
    onSave,
    placeholder = 'Click to edit this section...'
}: TiptapInlineEditorProps) {
    console.log('[SectionClickBug] TiptapInlineEditor rendered with initialValue:', initialValue);

    const [isFocused, setIsFocused] = useState(false);
    const [lastSavedContent, setLastSavedContent] = useState(initialValue);

    const editor = useEditor({
        extensions: [
            StarterKit.configure({
                codeBlock: {
                    HTMLAttributes: {
                        class: 'code-block-wrapper'
                    }
                }
            }),
            Markdown.configure({
                html: false,
                transformCopiedText: true,
                transformPastedText: true
            })
        ],
        content: initialValue,
        editorProps: {
            attributes: {
                class: 'markdown-content prose prose-sm max-w-none focus:outline-none',
                'data-placeholder': placeholder
            }
        },
        immediatelyRender: false,
        // Auto-save on content change
        onUpdate: ({ editor }) => {
            console.log('[SectionClickBug] Editor content updated');
            if (onSave) {
                // Debounce auto-save to avoid too frequent saves
                setTimeout(() => {
                    const markdownContent = editor.storage.markdown.getMarkdown();
                    if (markdownContent !== lastSavedContent) {
                        console.log('[SectionClickBug] Auto-saving content:', markdownContent);
                        onSave(markdownContent);
                        setLastSavedContent(markdownContent);
                    }
                }, 1000);
            }
        },
        onFocus: ({ editor }) => {
            console.log('[SectionClickBug] Editor focused');
            setIsFocused(true);
        },
        onBlur: ({ editor }) => {
            console.log('[SectionClickBug] Editor lost focus, auto-saving');
            setIsFocused(false);
            if (onSave) {
                const markdownContent = editor.storage.markdown.getMarkdown();
                if (markdownContent !== lastSavedContent) {
                    console.log('[SectionClickBug] Saving content on blur:', markdownContent);
                    onSave(markdownContent);
                    setLastSavedContent(markdownContent);
                }
            }
        }
    });

    // Update content when prop changes
    useEffect(() => {
        if (editor && initialValue !== editor.storage.markdown.getMarkdown()) {
            console.log('[SectionClickBug] Updating editor content:', initialValue);
            editor.commands.setContent(initialValue);
            setLastSavedContent(initialValue);
        }
    }, [editor, initialValue]);

    if (!editor) {
        console.log('[SectionClickBug] Editor not ready yet');
        return null;
    }

    const isEmpty = !initialValue || initialValue.trim() === '';
    const showPlaceholder = isEmpty && !isFocused;

    return (
        <div
            className='tiptap-inline-editor nodrag w-full max-w-full overflow-hidden'
            // Prevent XYFlow from capturing mouse events on the editor
            onMouseDown={e => {
                console.log('[SectionClickBug] Editor mouseDown - stopping propagation');
                e.stopPropagation();
            }}
            onMouseMove={e => {
                e.stopPropagation();
            }}
            onMouseUp={e => {
                e.stopPropagation();
            }}
            onClick={e => {
                console.log('[SectionClickBug] Editor container clicked');
                e.stopPropagation();
                if (!isFocused) {
                    editor.commands.focus();
                }
            }}
            // Make this area non-draggable for XYFlow
            draggable={false}
            style={{
                pointerEvents: 'auto',
                userSelect: 'text',
                maxWidth: '100%',
                width: '100%'
            }}
        >
            {showPlaceholder ? (
                <div
                    className='min-h-[20px] cursor-text rounded p-0 text-xs italic text-gray-400 hover:bg-gray-100/50'
                    style={{ lineHeight: '1.5' }}
                    onClick={e => {
                        console.log('[SectionClickBug] Placeholder clicked, focusing editor');
                        e.stopPropagation();
                        editor.commands.focus();
                    }}
                    onMouseDown={e => e.stopPropagation()}
                >
                    {placeholder}
                </div>
            ) : (
                <EditorContent
                    editor={editor}
                    className='min-h-[20px] w-full max-w-full cursor-text overflow-hidden break-words rounded p-0 hover:bg-gray-100/50'
                    style={{
                        lineHeight: '1.5',
                        pointerEvents: 'auto',
                        userSelect: 'text',
                        maxWidth: '100%',
                        width: '100%'
                    }}
                    onMouseDown={e => {
                        console.log(
                            '[SectionClickBug] EditorContent mouseDown - stopping propagation'
                        );
                        e.stopPropagation();
                    }}
                    onClick={e => {
                        console.log('[SectionClickBug] EditorContent clicked');
                        e.stopPropagation();
                    }}
                />
            )}
        </div>
    );
}

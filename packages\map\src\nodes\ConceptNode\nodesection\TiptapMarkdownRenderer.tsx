import React from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Markdown } from 'tiptap-markdown';

interface TiptapMarkdownRendererProps {
    content?: string;
    onClick?: (event: React.MouseEvent, clickPosition?: { x: number; y: number }) => void;
    className?: string;
    placeholder?: string;
}

export function TiptapMarkdownRenderer({
    content = '',
    onClick,
    className = '',
    placeholder = 'Click to edit this section...'
}: TiptapMarkdownRendererProps) {
    console.log('[SectionClickBug] TiptapMarkdownRenderer rendered with content:', content);

    const editor = useEditor({
        extensions: [
            StarterKit.configure({
                // Configure extensions for read-only display
                codeBlock: {
                    HTMLAttributes: {
                        class: 'code-block-wrapper'
                    }
                }
            }),
            Markdown.configure({
                html: false,
                transformCopiedText: false,
                transformPastedText: false
            })
        ],
        content: content,
        editable: false,
        editorProps: {
            attributes: {
                class: 'tiptap-renderer-content focus:outline-none'
            }
        },
        immediatelyRender: false
    });

    // Update content when prop changes
    React.useEffect(() => {
        if (editor && content !== editor.storage.markdown.getMarkdown()) {
            console.log('[SectionClickBug] Updating renderer content:', content);
            editor.commands.setContent(content);
        }
    }, [editor, content]);

    const handleClick = (event: React.MouseEvent) => {
        console.log('[SectionClickBug] Renderer clicked, event:', event);
        event.preventDefault();
        event.stopPropagation();

        if (onClick) {
            console.log('[SectionClickBug] Calling onClick handler');
            // Get the click position relative to the viewport
            const clickPosition = {
                x: event.clientX,
                y: event.clientY
            };
            console.log('[SectionClickBug] Click position:', clickPosition);
            onClick(event, clickPosition);
        }
    };

    if (!editor) {
        console.log('[SectionClickBug] Renderer editor not ready yet');
        return null;
    }

    const isEmpty = !content || content.trim() === '';
    console.log('[SectionClickBug] Content isEmpty:', isEmpty);

    return (
        <div
            onClick={handleClick}
            className={`min-h-[20px] w-full max-w-full cursor-text rounded p-0 hover:bg-gray-100/50 ${className}`}
            style={{ lineHeight: '1.5' }}
        >
            {isEmpty ? (
                <span className='text-xs italic text-gray-400' onClick={handleClick}>
                    {placeholder}
                </span>
            ) : (
                <div onClick={handleClick} className='w-full max-w-full'>
                    <EditorContent
                        editor={editor}
                        className='pointer-events-none w-full max-w-full overflow-hidden break-words'
                    />
                </div>
            )}
        </div>
    );
}

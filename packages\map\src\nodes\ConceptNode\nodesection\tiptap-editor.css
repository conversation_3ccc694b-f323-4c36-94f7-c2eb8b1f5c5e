/* Tiptap Editor Styles */

/* Base editor styles */
.tiptap-editor .ProseMirror {
    outline: none;
    width: 100%;
    max-width: 100%;
    min-height: 20px;
    padding: 0;
    font-size: 0.875rem;
    line-height: 1.5;
    color: #1f2937;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Paragraph styles */
.tiptap-editor .ProseMirror p {
    margin: 0.5em 0;
}

.tiptap-editor .ProseMirror p:first-child {
    margin-top: 0;
}

.tiptap-editor .ProseMirror p:last-child {
    margin-bottom: 0;
}

/* List styles */
.tiptap-editor .ProseMirror ul,
.tiptap-editor .ProseMirror ol {
    padding-left: 1.5em;
    margin: 0.5em 0;
}

/* Heading styles */
.tiptap-editor .ProseMirror h1,
.tiptap-editor .ProseMirror h2,
.tiptap-editor .ProseMirror h3,
.tiptap-editor .ProseMirror h4,
.tiptap-editor .ProseMirror h5,
.tiptap-editor .ProseMirror h6 {
    margin: 1em 0 0.5em;
    font-weight: 600;
}

.tiptap-editor .ProseMirror h1 {
    font-size: 1.25rem;
}

.tiptap-editor .ProseMirror h2 {
    font-size: 1.15rem;
}

.tiptap-editor .ProseMirror h3 {
    font-size: 1.05rem;
}

/* Code styles */
.tiptap-editor .ProseMirror code {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 0.25em;
    padding: 0.1em 0.3em;
    font-family: monospace;
    word-break: break-all;
}

.tiptap-editor .ProseMirror pre {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 0.25em;
    padding: 0.5em;
    font-family: monospace;
    overflow-x: auto;
    max-width: 100%;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.tiptap-editor .ProseMirror .code-block-wrapper {
    max-width: 100%;
    overflow: hidden;
}

/* Blockquote styles */
.tiptap-editor .ProseMirror blockquote {
    border-left: 3px solid #e5e7eb;
    padding-left: 1em;
    margin-left: 0;
    margin-right: 0;
    font-style: italic;
}

/* Horizontal rule styles */
.tiptap-editor .ProseMirror hr {
    border: none;
    border-top: 2px solid #e5e7eb;
    margin: 1em 0;
}

/* Link styles */
.tiptap-editor .ProseMirror a {
    color: #3b82f6;
    text-decoration: underline;
}

/* Read-only markdown content styles */
.markdown-content .ProseMirror {
    outline: none;
    width: 100%;
    max-width: 100%;
    min-height: 20px;
    padding: 0;
    font-size: 0.875rem;
    line-height: 1.5;
    color: #1f2937;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.markdown-content .ProseMirror p {
    margin: 0.5em 0;
}

.markdown-content .ProseMirror p:first-child {
    margin-top: 0;
}

.markdown-content .ProseMirror p:last-child {
    margin-bottom: 0;
}

.markdown-content .ProseMirror ul,
.markdown-content .ProseMirror ol {
    padding-left: 1.5em;
    margin: 0.5em 0;
}

.markdown-content .ProseMirror h1,
.markdown-content .ProseMirror h2,
.markdown-content .ProseMirror h3,
.markdown-content .ProseMirror h4,
.markdown-content .ProseMirror h5,
.markdown-content .ProseMirror h6 {
    margin: 1em 0 0.5em;
    font-weight: 600;
}

.markdown-content .ProseMirror h1 {
    font-size: 1.25rem;
}

.markdown-content .ProseMirror h2 {
    font-size: 1.15rem;
}

.markdown-content .ProseMirror h3 {
    font-size: 1.05rem;
}

.markdown-content .ProseMirror code {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 0.25em;
    padding: 0.1em 0.3em;
    font-family: monospace;
    word-break: break-all;
}

.markdown-content .ProseMirror pre {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 0.25em;
    padding: 0.5em;
    font-family: monospace;
    overflow-x: auto;
    max-width: 100%;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.markdown-content .ProseMirror .code-block-wrapper {
    max-width: 100%;
    overflow: hidden;
}

.markdown-content .ProseMirror blockquote {
    border-left: 3px solid #e5e7eb;
    padding-left: 1em;
    margin-left: 0;
    margin-right: 0;
    font-style: italic;
}

.markdown-content .ProseMirror hr {
    border: none;
    border-top: 2px solid #e5e7eb;
    margin: 1em 0;
}

.markdown-content .ProseMirror a {
    color: #3b82f6;
    text-decoration: underline;
}

/* Tiptap v3 specific styles */
.tiptap-editor .is-editor-empty:first-child::before,
.markdown-content .is-editor-empty:first-child::before {
    content: attr(data-placeholder);
    float: left;
    color: #adb5bd;
    pointer-events: none;
    height: 0;
}

/* Ensure proper focus styles */
.tiptap-editor .ProseMirror:focus,
.markdown-content .ProseMirror:focus {
    outline: none;
}

/* Ensure proper cursor in editable areas */
.tiptap-editor .ProseMirror {
    cursor: text;
}

/* Ensure proper cursor in read-only areas */
.markdown-content .ProseMirror {
    cursor: text;
}

/* Inline editor specific styles */
.tiptap-inline-editor {
    cursor: text;
    max-width: 100%;
    width: 100%;
    overflow: hidden;
}

.tiptap-inline-editor .ProseMirror {
    cursor: text;
    max-width: 100%;
    width: 100%;
    overflow: hidden;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.tiptap-inline-editor:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Ensure all content respects container width */
.tiptap-inline-editor .ProseMirror * {
    max-width: 100%;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

/* Specific handling for code blocks and pre elements */
.tiptap-inline-editor .ProseMirror pre,
.tiptap-inline-editor .ProseMirror code {
    max-width: 100%;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}
